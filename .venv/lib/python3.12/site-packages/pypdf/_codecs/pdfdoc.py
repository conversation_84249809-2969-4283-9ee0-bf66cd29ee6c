# PDFDocEncoding Character Set: Table D.2 of PDF Reference 1.7
# C.1 Predefined encodings sorted by character name of another PDF reference
# Some indices have '\u0000' although they should have something else:
# 22: should be '\u0017'
_pdfdoc_encoding = [
    "\u0000",
    "\u0001",
    "\u0002",
    "\u0003",
    "\u0004",
    "\u0005",
    "\u0006",
    "\u0007",  # 0 -  7
    "\u0008",
    "\u0009",
    "\u000a",
    "\u000b",
    "\u000c",
    "\u000d",
    "\u000e",
    "\u000f",  # 8 - 15
    "\u0010",
    "\u0011",
    "\u0012",
    "\u0013",
    "\u0014",
    "\u0015",
    "\u0000",
    "\u0017",  # 16 - 23
    "\u02d8",
    "\u02c7",
    "\u02c6",
    "\u02d9",
    "\u02dd",
    "\u02db",
    "\u02da",
    "\u02dc",  # 24 - 31
    "\u0020",
    "\u0021",
    "\u0022",
    "\u0023",
    "\u0024",
    "\u0025",
    "\u0026",
    "\u0027",  # 32 - 39
    "\u0028",
    "\u0029",
    "\u002a",
    "\u002b",
    "\u002c",
    "\u002d",
    "\u002e",
    "\u002f",  # 40 - 47
    "\u0030",
    "\u0031",
    "\u0032",
    "\u0033",
    "\u0034",
    "\u0035",
    "\u0036",
    "\u0037",  # 48 - 55
    "\u0038",
    "\u0039",
    "\u003a",
    "\u003b",
    "\u003c",
    "\u003d",
    "\u003e",
    "\u003f",  # 56 - 63
    "\u0040",
    "\u0041",
    "\u0042",
    "\u0043",
    "\u0044",
    "\u0045",
    "\u0046",
    "\u0047",  # 64 - 71
    "\u0048",
    "\u0049",
    "\u004a",
    "\u004b",
    "\u004c",
    "\u004d",
    "\u004e",
    "\u004f",  # 72 - 79
    "\u0050",
    "\u0051",
    "\u0052",
    "\u0053",
    "\u0054",
    "\u0055",
    "\u0056",
    "\u0057",  # 80 - 87
    "\u0058",
    "\u0059",
    "\u005a",
    "\u005b",
    "\u005c",
    "\u005d",
    "\u005e",
    "\u005f",  # 88 - 95
    "\u0060",
    "\u0061",
    "\u0062",
    "\u0063",
    "\u0064",
    "\u0065",
    "\u0066",
    "\u0067",  # 96 - 103
    "\u0068",
    "\u0069",
    "\u006a",
    "\u006b",
    "\u006c",
    "\u006d",
    "\u006e",
    "\u006f",  # 104 - 111
    "\u0070",
    "\u0071",
    "\u0072",
    "\u0073",
    "\u0074",
    "\u0075",
    "\u0076",
    "\u0077",  # 112 - 119
    "\u0078",
    "\u0079",
    "\u007a",
    "\u007b",
    "\u007c",
    "\u007d",
    "\u007e",
    "\u0000",  # 120 - 127
    "\u2022",
    "\u2020",
    "\u2021",
    "\u2026",
    "\u2014",
    "\u2013",
    "\u0192",
    "\u2044",  # 128 - 135
    "\u2039",
    "\u203a",
    "\u2212",
    "\u2030",
    "\u201e",
    "\u201c",
    "\u201d",
    "\u2018",  # 136 - 143
    "\u2019",
    "\u201a",
    "\u2122",
    "\ufb01",
    "\ufb02",
    "\u0141",
    "\u0152",
    "\u0160",  # 144 - 151
    "\u0178",
    "\u017d",
    "\u0131",
    "\u0142",
    "\u0153",
    "\u0161",
    "\u017e",
    "\u0000",  # 152 - 159
    "\u20ac",
    "\u00a1",
    "\u00a2",
    "\u00a3",
    "\u00a4",
    "\u00a5",
    "\u00a6",
    "\u00a7",  # 160 - 167
    "\u00a8",
    "\u00a9",
    "\u00aa",
    "\u00ab",
    "\u00ac",
    "\u0000",
    "\u00ae",
    "\u00af",  # 168 - 175
    "\u00b0",
    "\u00b1",
    "\u00b2",
    "\u00b3",
    "\u00b4",
    "\u00b5",
    "\u00b6",
    "\u00b7",  # 176 - 183
    "\u00b8",
    "\u00b9",
    "\u00ba",
    "\u00bb",
    "\u00bc",
    "\u00bd",
    "\u00be",
    "\u00bf",  # 184 - 191
    "\u00c0",
    "\u00c1",
    "\u00c2",
    "\u00c3",
    "\u00c4",
    "\u00c5",
    "\u00c6",
    "\u00c7",  # 192 - 199
    "\u00c8",
    "\u00c9",
    "\u00ca",
    "\u00cb",
    "\u00cc",
    "\u00cd",
    "\u00ce",
    "\u00cf",  # 200 - 207
    "\u00d0",
    "\u00d1",
    "\u00d2",
    "\u00d3",
    "\u00d4",
    "\u00d5",
    "\u00d6",
    "\u00d7",  # 208 - 215
    "\u00d8",
    "\u00d9",
    "\u00da",
    "\u00db",
    "\u00dc",
    "\u00dd",
    "\u00de",
    "\u00df",  # 216 - 223
    "\u00e0",
    "\u00e1",
    "\u00e2",
    "\u00e3",
    "\u00e4",
    "\u00e5",
    "\u00e6",
    "\u00e7",  # 224 - 231
    "\u00e8",
    "\u00e9",
    "\u00ea",
    "\u00eb",
    "\u00ec",
    "\u00ed",
    "\u00ee",
    "\u00ef",  # 232 - 239
    "\u00f0",
    "\u00f1",
    "\u00f2",
    "\u00f3",
    "\u00f4",
    "\u00f5",
    "\u00f6",
    "\u00f7",  # 240 - 247
    "\u00f8",
    "\u00f9",
    "\u00fa",
    "\u00fb",
    "\u00fc",
    "\u00fd",
    "\u00fe",
    "\u00ff",  # 248 - 255
]

assert len(_pdfdoc_encoding) == 256
