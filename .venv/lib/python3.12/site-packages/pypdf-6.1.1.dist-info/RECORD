pypdf-6.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pypdf-6.1.1.dist-info/METADATA,sha256=BJ4utPFKpfB6wjVMOpKC20O3yEX8vqzoqj1pO16-mAo,7149
pypdf-6.1.1.dist-info/RECORD,,
pypdf-6.1.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf-6.1.1.dist-info/WHEEL,sha256=G2gURzTEtmeR8nrdXUJfNiB3VYVxigPQ-bEQujpNiNs,82
pypdf-6.1.1.dist-info/licenses/LICENSE,sha256=qXrCMOXzPvEKU2eoUOsB-R8aCwZONHQsd5TSKUVX9SQ,1605
pypdf/__init__.py,sha256=YS_1ZrQ3jBPHsRgMstqJrAts3lUApj_lMOMK5qiLG5w,1283
pypdf/__pycache__/__init__.cpython-312.pyc,,
pypdf/__pycache__/_cmap.cpython-312.pyc,,
pypdf/__pycache__/_doc_common.cpython-312.pyc,,
pypdf/__pycache__/_encryption.cpython-312.pyc,,
pypdf/__pycache__/_font.cpython-312.pyc,,
pypdf/__pycache__/_page.cpython-312.pyc,,
pypdf/__pycache__/_page_labels.cpython-312.pyc,,
pypdf/__pycache__/_protocols.cpython-312.pyc,,
pypdf/__pycache__/_reader.cpython-312.pyc,,
pypdf/__pycache__/_utils.cpython-312.pyc,,
pypdf/__pycache__/_version.cpython-312.pyc,,
pypdf/__pycache__/_writer.cpython-312.pyc,,
pypdf/__pycache__/_xobj_image_helpers.cpython-312.pyc,,
pypdf/__pycache__/constants.cpython-312.pyc,,
pypdf/__pycache__/errors.cpython-312.pyc,,
pypdf/__pycache__/filters.cpython-312.pyc,,
pypdf/__pycache__/pagerange.cpython-312.pyc,,
pypdf/__pycache__/papersizes.cpython-312.pyc,,
pypdf/__pycache__/types.cpython-312.pyc,,
pypdf/__pycache__/xmp.cpython-312.pyc,,
pypdf/_cmap.py,sha256=GAN507qMDdmdnl-13lqQHkUV_z61JLM3lOhma7ZGpug,18919
pypdf/_codecs/__init__.py,sha256=PF1KlsLWCOF0cgdqns7G4X-l3zq5_OnZePw7RFIn1bE,1645
pypdf/_codecs/__pycache__/__init__.cpython-312.pyc,,
pypdf/_codecs/__pycache__/_codecs.cpython-312.pyc,,
pypdf/_codecs/__pycache__/adobe_glyphs.cpython-312.pyc,,
pypdf/_codecs/__pycache__/core_fontmetrics.cpython-312.pyc,,
pypdf/_codecs/__pycache__/pdfdoc.cpython-312.pyc,,
pypdf/_codecs/__pycache__/std.cpython-312.pyc,,
pypdf/_codecs/__pycache__/symbol.cpython-312.pyc,,
pypdf/_codecs/__pycache__/zapfding.cpython-312.pyc,,
pypdf/_codecs/_codecs.py,sha256=6WwcaOjz5GoIpIAJ3_RRbQd89bITxM9ik0KrKuDr63g,10090
pypdf/_codecs/adobe_glyphs.py,sha256=t3cDFPDqwIz1w9B0gdVzjdc8eEK9AuRjk5f7laEw_fY,447213
pypdf/_codecs/core_fontmetrics.py,sha256=qQvNRQi8V8FOBmSwGcsak4qyl9cQ80cDjbpD5TvhuBg,113269
pypdf/_codecs/pdfdoc.py,sha256=xfSvMFYsvxuaSQ0Uu9vZDKaB0Wu85h1uCiB1i9rAcUU,4269
pypdf/_codecs/std.py,sha256=DyQMuEpAGEpS9uy1jWf4cnj-kqShPOAij5sI7Q1YD8E,2630
pypdf/_codecs/symbol.py,sha256=nIaGQIlhWCJiPMHrwUlmGHH-_fOXyEKvguRmuKXcGAk,3734
pypdf/_codecs/zapfding.py,sha256=PQxjxRC616d41xF3exVxP1W8nM4QrZfjO3lmtLxpE_s,3742
pypdf/_crypt_providers/__init__.py,sha256=K3Z6AuXhXVeXgLet-Tukq2gt9H66OgdupsvxIS1CmkI,3054
pypdf/_crypt_providers/__pycache__/__init__.cpython-312.pyc,,
pypdf/_crypt_providers/__pycache__/_base.cpython-312.pyc,,
pypdf/_crypt_providers/__pycache__/_cryptography.cpython-312.pyc,,
pypdf/_crypt_providers/__pycache__/_fallback.cpython-312.pyc,,
pypdf/_crypt_providers/__pycache__/_pycryptodome.cpython-312.pyc,,
pypdf/_crypt_providers/_base.py,sha256=_f53Mj6vivhEZMQ4vNxN5G0IOgFY-n5_leke0c_qiNU,1711
pypdf/_crypt_providers/_cryptography.py,sha256=zT3WmbPzesvgHRkGcKAldqJ24MY3BwZViVbSc55Zxhw,4557
pypdf/_crypt_providers/_fallback.py,sha256=vsYoowR1YCAV_q-HrdIZhkUcrCb6HvRBNMYm03QtCU8,3334
pypdf/_crypt_providers/_pycryptodome.py,sha256=U1aQZ9iYBrZo-hKCjJUhGOPhwEFToiitowQ316TNrrA,3381
pypdf/_doc_common.py,sha256=k0-Wof5dz2VtEnZ5lJWBCM7kD71LeB8ze6RwX3mmPt0,52385
pypdf/_encryption.py,sha256=-LwFEKfhL3B10afkco6fXx-EqtjoXf67pAUgH2VBfDw,48762
pypdf/_font.py,sha256=yfiB-jFmN2eHWXavPJ6d4Ib8kQEt0d39euPAiUAF1ZY,1354
pypdf/_page.py,sha256=nJGQZLDSrFtAyLtQACoypokjDNL9Fol_m5VBokLOdDk,88927
pypdf/_page_labels.py,sha256=_HXqgEhSLTH_mMhy8m4QAOzIOHRQLV6_lYvg81-l9hI,8546
pypdf/_protocols.py,sha256=7qz92LVdPrYkSpdUPpAp9U4GW5jxNBTfVcpUWwUhEOo,2123
pypdf/_reader.py,sha256=_Z_YEpkYSy0Nk96oC-1Mj76Kgmrb0vHYLtz6ML9MwTU,51731
pypdf/_text_extraction/__init__.py,sha256=PNM5qZfVEoNw7GzBdrbQ_coKoyzBHyIp8KThQuY9pqE,8519
pypdf/_text_extraction/__pycache__/__init__.cpython-312.pyc,,
pypdf/_text_extraction/__pycache__/_text_extractor.cpython-312.pyc,,
pypdf/_text_extraction/_layout_mode/__init__.py,sha256=k1tN46gDX1zhAatD8oTGMuCJUp-pgbHjyQ8H6axXRgU,338
pypdf/_text_extraction/_layout_mode/__pycache__/__init__.cpython-312.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_fixed_width_page.cpython-312.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_font.cpython-312.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_font_widths.cpython-312.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_text_state_manager.cpython-312.pyc,,
pypdf/_text_extraction/_layout_mode/__pycache__/_text_state_params.cpython-312.pyc,,
pypdf/_text_extraction/_layout_mode/_fixed_width_page.py,sha256=Q8D07sfoE5BmHCPm6CX2__z29CHx0i0SNFUFoaUd2f4,14923
pypdf/_text_extraction/_layout_mode/_font.py,sha256=vgx2vc8CQLqs1RdLlHu9liWXBnJM4DGJQdEuD5BW3uM,7068
pypdf/_text_extraction/_layout_mode/_font_widths.py,sha256=Hfgsd2ftGw8Ajl7IcwNIlfLYnum-ekaadfwErcUdWtI,4265
pypdf/_text_extraction/_layout_mode/_text_state_manager.py,sha256=0-SPCYnAh0Q5B25mw88lMfrbqEcsKcnDu-2h9q6y5UA,8214
pypdf/_text_extraction/_layout_mode/_text_state_params.py,sha256=5-EkV3V37w3zG0XQdpzXr9YMQnOMXa6nJ1B3HhT9gb8,5306
pypdf/_text_extraction/_text_extractor.py,sha256=pmtU8-uA006Daw7j2Rvdl63u68uLVnUQDRZw267IGy4,16622
pypdf/_utils.py,sha256=v579jJEHn-JophTC4Ej2MBFTEoQGitPWs_d507pyS6g,20194
pypdf/_version.py,sha256=TogwtvfScmmY5i06w1pmsYL28pcWyPETM_YMFRujQQY,22
pypdf/_writer.py,sha256=dqkNr6Ubq_QQK2MN_XOU8_Eihas2wVcHqF0Tx-9qHyY,134766
pypdf/_xobj_image_helpers.py,sha256=RkpAet_GNnZA2V0hqt_i_K-VyWNsLiSMHCbwz9gPVlk,14702
pypdf/annotations/__init__.py,sha256=f2k_-jAn39CCB27KxQ_e93GinnzkAHbUnnSeGJl1jyE,990
pypdf/annotations/__pycache__/__init__.cpython-312.pyc,,
pypdf/annotations/__pycache__/_base.cpython-312.pyc,,
pypdf/annotations/__pycache__/_markup_annotations.cpython-312.pyc,,
pypdf/annotations/__pycache__/_non_markup_annotations.cpython-312.pyc,,
pypdf/annotations/_base.py,sha256=eeoc9v2w15jAUhKXj48l1bB66YgBgV-2v5IIUJH-vws,961
pypdf/annotations/_markup_annotations.py,sha256=c0WevBk7HGUlbb_LTjtKk6C3TS55jL53X0Gff0cwsU8,9589
pypdf/annotations/_non_markup_annotations.py,sha256=Z2IUvcCOcTcpJhSXrex_9riYM2D64XxFQ_vac10BNRU,3649
pypdf/constants.py,sha256=nS44NtURz4IzFAUdzGvIW_H-v2vcFjszA7YFDC3nhxw,23018
pypdf/errors.py,sha256=Bw1W9hxOsDgwqwU6YoQ2l0-JiUyTq6l5QjVCr-W4GFA,1947
pypdf/filters.py,sha256=P2l4Db4sQMyRyL_T2JCT_MndM3se7Z35ajUqaBXjbSY,33983
pypdf/generic/__init__.py,sha256=VrqdYftQECePDU2rXVMgEqRaYFR8zOV_fvJgo19x_uw,3468
pypdf/generic/__pycache__/__init__.cpython-312.pyc,,
pypdf/generic/__pycache__/_base.cpython-312.pyc,,
pypdf/generic/__pycache__/_data_structures.cpython-312.pyc,,
pypdf/generic/__pycache__/_files.cpython-312.pyc,,
pypdf/generic/__pycache__/_fit.cpython-312.pyc,,
pypdf/generic/__pycache__/_image_inline.cpython-312.pyc,,
pypdf/generic/__pycache__/_link.cpython-312.pyc,,
pypdf/generic/__pycache__/_outline.cpython-312.pyc,,
pypdf/generic/__pycache__/_rectangle.cpython-312.pyc,,
pypdf/generic/__pycache__/_utils.cpython-312.pyc,,
pypdf/generic/__pycache__/_viewerpref.cpython-312.pyc,,
pypdf/generic/_base.py,sha256=dq7i_VU0VM_6JzlEv1kxq_M6RhZt9Asz-2RejMjZXqE,31758
pypdf/generic/_data_structures.py,sha256=df2dL9O6uqiIQdOqzzMDVhg_yEYUMI-cKGGCmbzpO4c,63214
pypdf/generic/_files.py,sha256=NtSkRo6JBgisi4QOyrVneO891boVsuY25hRwij6X9RA,16238
pypdf/generic/_fit.py,sha256=X_iADJj1YY4PUStS7rFWC2xR2LUVSvKtUAky0AFAIDM,5515
pypdf/generic/_image_inline.py,sha256=B9jqOt_navgmjekp7BqNpu72SMccdHjNCo4_Jtez15w,11478
pypdf/generic/_link.py,sha256=ibdLhdU0mP_phneaJs-CzUDErkJuqnMT6TsQoHNOYiE,4951
pypdf/generic/_outline.py,sha256=qKbMX42OWfqnopIiE6BUy6EvdTLGe3ZtjaiWN85JpaY,1094
pypdf/generic/_rectangle.py,sha256=lOqSfFivQxgBN9LU9aqHoxPH8aCPTDUNgRZsNEUd6fc,3785
pypdf/generic/_utils.py,sha256=vTDAesfG-cJNDKilz_kbgFodAITzd5ejppWHGjvConk,7258
pypdf/generic/_viewerpref.py,sha256=6a_s0Avm9-XvV0wqxiW23cE92qK98ry3y6EPjfsFSdo,6758
pypdf/pagerange.py,sha256=2bt21jQZm-9aq2bVf3TXuH8_wGVx7b9T6UrMFXCEJhQ,7108
pypdf/papersizes.py,sha256=6Tz5sfNN_3JOUapY83U-lakohnpXYA0hSEQNmOVLFL8,1413
pypdf/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf/types.py,sha256=sJ7wHzk7ER_CJ7kP-s8u9axFnkCXnFpr8nzcj1AxTas,1915
pypdf/xmp.py,sha256=gqh3IlgTNP7ZuyhvE59p2tsMvu4adGkq0G8RDg0OtQw,29238
