import pypdf
from pypdf import Pdf<PERSON>rite<PERSON>, Pdf<PERSON><PERSON><PERSON>

def combine_pdfs(pdf1_path, pdf2_path, output_path):
    """
    Combine two PDF files into one
    
    Args:
        pdf1_path (str): Path to first PDF file
        pdf2_path (str): Path to second PDF file  
        output_path (str): Path for the combined output PDF
    """
    # Create a PDF writer object
    pdf_writer = PdfWriter()
    
    # Read and add pages from first PDF
    with open(pdf1_path, 'rb') as pdf1_file:
        pdf1_reader = PdfReader(pdf1_file)
        for page_num in range(len(pdf1_reader.pages)):
            page = pdf1_reader.pages[page_num]
            pdf_writer.add_page(page)
    
    # Read and add pages from second PDF
    with open(pdf2_path, 'rb') as pdf2_file:
        pdf2_reader = PdfReader(pdf2_file)
        for page_num in range(len(pdf2_reader.pages)):
            page = pdf2_reader.pages[page_num]
            pdf_writer.add_page(page)
    
    # Write the combined PDF to output file
    with open(output_path, 'wb') as output_file:
        pdf_writer.write(output_file)
    
    print(f"Successfully combined PDFs! Output saved as: {output_path}")

# Example usage:
if __name__ == "__main__":
    # Replace these with your actual file paths
    pdf1 = "document1.pdf"
    pdf2 = "document2.pdf"
    output = "combined_document.pdf"
    
    try:
        combine_pdfs(pdf1, pdf2, output)
    except FileNotFoundError as e:
        print(f"Error: Could not find file - {e}")
    except Exception as e:
        print(f"Error combining PDFs: {e}")
